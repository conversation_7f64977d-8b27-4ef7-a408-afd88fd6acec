import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  routes: [
    // 移动事件明细报表
    {
      path: '/hmes/transaction-report/mobile-event-detail-report',
      component: '@/routes/transactionReport/MobileEventDetailReport',
    },
    // 班次装配实绩报表
    {
      path: '/hmes/workshop/assemble-report',
      component: '@/routes/workshop/AssembleReport',
    },
    // 班次作业实绩报表
    {
      path: '/hmes/workshop/job-report',
      component: '@/routes/workshop/JobReport',
    },
    // 容器管理平台-MES
    {
      path: '/hmes/product/container-management-platform',
      priority: 10,
      routes: [
        {
          priority: 10,
          path: '/hmes/product/container-management-platform/list',
          component: '@/routes/product/ContainerManagePlatform',
        },
      ],
    },
    // 事件处理
    {
      path: '/hmes/event/query',
      component: '../routes/Event/EventQuery',
      priority: 10,
    },
    // 返修工单条码绑定
    {
      path: '/hmes/rebate-workOrder-barcode-binding',
      routes: [
        {
          path: '/hmes/rebate-workOrder-barcode-binding/list',
          component: '@/routes/RebateWorkOrderBarcodeBinding',
        },
      ],
    },
    // 返修工单条码绑定-OA
    {
      path: '/pub/hmes/rebate-workOrder-barcode-binding-oa',
      // authorized: true,
      routes: [
        {
          title: '返修工单条码绑定',
          path: `/pub/hmes/rebate-workOrder-barcode-binding-oa/list/`,
          component: '@/routes/RebateWorkOrderBarcodeBindingOA',
          // authorized: true,
        },
        {
          title: '返修工单条码绑定',
          path: `/pub/hmes/rebate-workOrder-barcode-binding-oa/list/:id`,
          component: '@/routes/RebateWorkOrderBarcodeBindingOA',
          // authorized: true,
        },
      ],
    },
    // 不良记录创建-MES
    {
      path: '/hmes/bad-record/platform-new',
      routes: [
        {
          path: '/hmes/bad-record/platform-new/list',
          component: '@/routes/badRecordCreate/Platform/List',
        },
        {
          path: '/hmes/bad-record/platform-new/detail/:id',
          component: '@/routes/badRecordCreate/Platform/Detail',
        },
        {
          path: '/hmes/bad-record/platform-new/print/:id',
          component: '@/routes/badRecordCreate/Platform/Print',
        },
      ],
    },
    // 工艺维护
    {
      path: '/hmes/process/technology',
      routes: [
        {
          path: '/hmes/process/technology/list',
          component: '@/routes/process/Technology/TechnologyList',
        },
        {
          path: '/hmes/process/technology/dist/:id',
          component: '@/routes/process/Technology/TechnologyDist',
        },
      ],
    },
    // 二次判定采集项维护
    {
      path: '/hmes/quadratic-decision/list',
      authorized: true,
      component: '@/routes/QuadraticDecision',
    },
    // 不良记录管理平台
    {
      path: '/hmes/bad-record/platform',
      priority: 10,
      routes: [
        {
          path: '/hmes/bad-record/platform/list',
          component: '@/routes/badRecord/Platform/List',
          priority: 10,
        },
        {
          path: '/hmes/bad-record/platform/detail/:id',
          priority: 10,
          component: '@/routes/badRecord/Platform/Detail',
        },
      ],
    },
    // 物料批管理平台
    {
      path: '/hmes/product/material-lot-traceability',
      priority: 1000,
      routes: [
        {
          path: '/hmes/product/material-lot-traceability/list/:timer?',
          component: '@/routes/product/MaterialLotTrace',
          priority: 1000,
        },
        {
          path: '/hmes/product/material-lot-traceability/detail/:id',
          component: '@/routes/product/MaterialLotTrace/MaterialLotTraceDetail',
          priority: 1000,
        },
      ],
    },
    // 用户权限维护
    {
      path: '/hmes/mes/user-rights',
      component: '../routes/hmes/UserRights',
      priority: 10,
    },
    // 消息处理查询
    {
      path: '/message/message-processing',
      title: '消息处理查询',
      component: '../routes/message/MessageProcessing',
      priority: 10,
    },
    // 浆料队列调整
    {
      path: '/hmes/adjustment-slurry-queue',
      routes: [
        {
          path: '/hmes/adjustment-slurry-queue/list',
          component: '../routes/AdjustmentSlurryQueue',
        },
      ],
    },
    // 设备接口调用记录
    {
      path: '/hmes/device-interface-call-record',
      routes: [
        {
          path: '/hmes/device-interface-call-record/list',
          component: '../routes/DeviceInterface',
        },
      ],
    },
    {
      title: '设备接口调用记录-MOM',
      path: '/hmes/device-interface-call-mom',
      routes: [
        {
          path: '/hmes/device-interface-call-mom/list',
          component: '../routes/DeviceInterfaceMom',
        },
      ],
    },
    // 编码规则维护
    {
      path: '/hmes/mes/maintain-number-new',
      routes: [
        {
          path: '/hmes/mes/maintain-number-new/list',
          component: '@/routes/MaintainNumber/MaintainNumberList',
          model: '@/models/hmes/maintainNumber',
        },
        {
          path: '/hmes/mes/maintain-number-new/detail/:id',
          component: '@/routes/MaintainNumber/MaintainNumberDetail',
          model: '@/models/hmes/maintainNumber',
        },
      ],
    },
    // 数据收集项维护
    {
      path: '/hmes/acquisition/new-data-item-new',
      routes: [
        {
          path: '/hmes/acquisition/new-data-item-new/list',
          component: '@/routes/acquisition/NewDataItem',
        },
        {
          path: '/hmes/acquisition/new-data-item-new/detail/:id',
          component: '@/routes/acquisition/NewDataItem/Detail',
        },
      ],
    },
    // 数据收集组维护
    {
      path: '/hmes/acquisition/data-collection-new',
      routes: [
        {
          path: '/hmes/acquisition/data-collection-new/list',
          component: '@/routes/acquisition/Collection/CollectionList',
        },
        {
          path: '/hmes/acquisition/data-collection-new/detail/:id',
          component: '@/routes/acquisition/Collection/CollectionDetail',
        },
      ],
    },
    // 物料防呆防错
    {
      path: '/hmes/material-prevent-error',
      routes: [
        {
          path: '/hmes/material-prevent-error/list',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList',
        },
        {
          path: '/hmes/material-prevent-error/detail/:id',
          component: '../routes/MaterialPreventError/MaterialPreventErrorDetail',
        },
        {
          path: '/hmes/material-prevent-error/import/:code',
          component: '../routes/MaterialPreventError/MaterialPreventErrorList/CommentImport',
        },
      ],
    },
    // 装配点维护
    {
      path: '/hmes/equipment-point-maintenance',
      routes: [
        {
          path: '/hmes/equipment-point-maintenance/list',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceList',
        },
        {
          path: '/hmes/equipment-point-maintenance/detail/:id',
          priority: 10,
          component: '../routes/EquipmentPointMaintenanceNew/EquipmentPointMaintenanceDetail',
        },
      ],
    },
    // 装配组维护
    {
      path: '/hmes/equipment-group-maintenance',
      routes: [
        {
          path: '/hmes/equipment-group-maintenance/list',
          component: '@/routes/EquipmentGroupMaintenance',
        },
        {
          path: '/hmes/equipment-group-maintenance/:id',
          component: '@/routes/EquipmentGroupMaintenance/Create',
        },
      ],
    },
    // 加工控制配置维护
    {
      path: '/hmes/processing-control-maintenance',
      // authorized: true,
      routes: [
        {
          path: '/hmes/processing-control-maintenance/list',
          component: '../routes/ProcessingControlMaintenance',
        },
      ],
    },
    // 国标码编码规则维护
    {
      path: '/hmes/national-coding-maintenance',
      // authorized: true,
      routes: [
        {
          title: '国标码编码规则维护',
          path: '/hmes/national-coding-maintenance/list',
          component: '../routes/NationalCodingMaintenance',
        },
        {
          path: '/hmes/national-coding-maintenance/:id',
          component: '../routes/NationalCodingMaintenance/detail',
        },
      ],
    },
    // 工单管理平台
    {
      path: '/hmes/workorder-management-platform',
      priority: 1000,
      routes: [
        {
          title: '工单管理平台',
          priority: 1000,
          path: '/hmes/workorder-management-platform/list',
          component: '../routes/WorkOrderManagementPlatform',
        },
      ],
    },

    // 工序反馈参数项配置
    {
      path: '/hmes/process-feedback-configuration',
      routes: [
        {
          path: '/hmes/process-feedback-configuration/list',
          component: '../routes/ProcessFeedbackConfiguration',
        },
      ],
    },

    // 发货报告打印项目维护
    {
      path: '/hmes/shipment-report-print-maintenance',
      routes: [
        {
          path: '/hmes/shipment-report-print-maintenance/list',
          component: '../routes/ShipmentReportPrintMaintenance',
        },
      ],
    },

    // 生产工艺折算系数维护
    {
      path: '/hmes/production-process-conversion',
      routes: [
        {
          title: '生产工艺折算系数维护',
          path: '/hmes/production-process-conversion/list',
          component: '../routes/ProductionProcessConversion',
        },
      ],
    },

    // 产品工艺保质期维护
    {
      path: '/hmes/product-process-shelf-maintenance',
      routes: [
        {
          path: '/hmes/product-process-shelf-maintenance/list',
          component: '../routes/ProductProcessShelfMaintenance',
        },
      ],
    },
    // 领退料工作台-新
    {
      path: '/hmes/receive/receive-return-new',
      priority: 1000,
      routes: [
        {
          priority: 1000,
          path: '/hmes/receive/receive-return-new/list',
          component: '@/routes/receive/ReceiveReturn',
        },
        {
          priority: 1000,
          path: '/hmes/receive/receive-return-new/detail/:id/:docType/:docTypeTag',
          component: '@/routes/receive/ReceiveReturn/Detail',
        },
      ],
    },
    // 产品工艺保质期批量更新
    {
      path: '/hmes/product-process-shelf-batch-update',
      routes: [
        {
          path: '/hmes/product-process-shelf-batch-update/list',
          component: '../routes/ProductProcessShelfBatchUpdate',
        },
      ],
    },
    // 执行作业管理
    {
      path: '/hmes/workshop/execute-operation-management',
      priority: 1000,
      routes: [
        {
          path: '/hmes/workshop/execute-operation-management/list',
          component: '@/routes/workshop/Execute/ExecuteList',
          priority: 1000,
        },
        {
          path: '/hmes/workshop/execute-operation-management/detail/:id',
          // authorized: true,
          component: '@/routes/workshop/Execute/ExecuteDetail',
          priority: 1000,
        },
      ],
    },
    // 生产指令管理
    {
      path: '/hmes/workshop/production-order-mgt',
      priority: 100,
      routes: [
        {
          path: '/hmes/workshop/production-order-mgt/list',
          priority: 100,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtList',
        },
        {
          path: '/hmes/workshop/production-order-mgt/detail/:id',
          priority: 100,
          component: '@/routes/workshop/ProductionOrderMgt/ProductionOrderMgtDetail',
        },
      ],
    },
    // 在制条码批量完工
    {
      path: '/hmes/wip-barcode-batch-complete',
      component: '@/routes/WipBarcodeBatchComplete',
    },
    // 设备锁定记录报表
    {
      path: '/hmes/device-lock-record-report',
      component: '@/routes/DeviceLockRecordReport',
    },
    // 物料PFEP属性维护
    {
      path: '/hmes/product/material-pfep-manager',
      routes: [
        {
          path: '/hmes/product/material-pfep-manager/list',
          component: '@/routes/Material/pfep/List/index',
        },
        {
          path: '/hmes/product/material-pfep-manager/detail/:id',
          component: '@/routes/Material/pfep/Detail/index',
        },
      ],
    },
    // 成本中心领退平台
    {
      path: '/hmes/in-library/miscellaneous-new',
      routes: [
        {
          path: '/hmes/in-library/miscellaneous-new/list',
          component: '@/routes/inLibrary/miscellaneous',
        },
        {
          path: '/hmes/in-library/miscellaneous-new/detail/:id',
          component: '@/routes/inLibrary/miscellaneous/Detail',
        },
      ],
    },
    // 制造工艺路线-c7n
    {
      path: '/hmes/new/manufacture-process/routes-c7n',
      priority: 100,
      routes: [
        {
          path: '/hmes/new/manufacture-process/routes-c7n/list',
          component: '@/routes/process/ProcessRouteC7n/index',
          priority: 100,
        },
        {
          path: '/hmes/new/manufacture-process/routes-c7n/dist/:id',
          component: '@/routes/process/ProcessRouteC7n/detail',
          priority: 100,
        },
      ],
    },
    // 报废审批配置维护
    {
      priority: 1000,
      path: '/hmes/scrap-approval-configuration',
      authorized: true,
      component: '@/routes/ScrapApprovalConfiguration',
    },
    // 制造装配清单
    {
      path: '/hmes/product/manufacture-list',
      priority: 100,
      routes: [
        {
          path: '/hmes/product/manufacture-list/list',
          component: '@/routes/product/C7nManufactureBom/index',
          priority: 100,
        },
        {
          path: '/hmes/product/manufacture-list/dist/:id',
          component: '@/routes/product/C7nManufactureBom/detail',
          priority: 100,
        },
      ],
    },
    {
      path: '/mes/deviceLock',
      title: '设备锁定',
      routes: [
        {
          path: '/mes/deviceLock/list',
          component: '@/routes/DeviceLock/list/listPage',
        },
        {
          path: '/mes/deviceLock/detail/:id',
          component: '@/routes/DeviceLock/list/detailPage',
        },
      ],
    },
    {
      path: '/pub/mes/deviceLock/:id',
      title: '设备锁定',
      component: '@/routes/DeviceLock/list/listPagePub',
    },
    {
      title: '账务处理平台',
      path: '/hmes/accountProcessing',
      routes: [
        {
          path: '/hmes/accountProcessing/list',
          component: '@/routes/AccountProcessing/List',
        },
        {
          path: '/hmes/accountProcessing/detail/:id',
          component: '@/routes/AccountProcessing/Detail',
        },
      ],
    },
    // 事务明细报表-新
    {
      priority: 1000,
      path: 'hmes/transaction-report/transaction-detail-report',
      component: '@/routes/transactionReport/TransactionDetailReport',
    },
    // 后备作业跳站记录查询报表
    {
      path: '/hmes/backup-work-eo-skips',
      routes: [
        {
          path: '/hmes/backup-work-eo-skips/list',
          component: '@/routes/BackupWorkEoSkips',
        },
      ],
    },
    // 卷底报废数量维护
    {
      path: '/hmes/roll-bottom-scrapping-maintenance',
      routes: [
        {
          path: '/hmes/roll-bottom-scrapping-maintenance/list',
          component: '../routes/RollBottomScrappingMaintenance',
        },
      ],
    },
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          '@': './src',
          '@components': 'hcm-components-front/lib/components',
          '@services': 'hcm-components-front/lib/services',
          '@utils': 'hcm-components-front/lib/utils',
          '@assets': 'hcm-components-front/lib/assets',
        },
      },
    ],
  ],
});
