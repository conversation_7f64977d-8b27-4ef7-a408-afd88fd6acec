/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-07-22 11:29:30
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-07-22 15:15:16
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\ScrapApprovalConfiguration\services\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

console.log(tenantId);
console.log(BASIC.HMES_BASIC);


// 保存
export function SaveScrapApproval() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-scrap-approval-sets/save/ui`,
    method: 'POST',
  };
}
