/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-07-22 13:41:47
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-07-23 11:21:25
 * @FilePath: \yp-mes-front\packages\key-hmes-front\src\routes\ScrapApprovalConfiguration\HistoryDrawer.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';

interface HistoryDrawerProps {
  ds: DataSet;
}

const HistoryDrawer: React.FC<HistoryDrawerProps> = ({ ds }) => {
  const columns: ColumnProps[] = [
    { name: 'siteCode', width: 100 },
    { name: 'approvalTypeMeaning', width: 120 },
    { name: 'prodLineCode', width: 120 },
    { name: 'prodLineName', width: 150 },
    { name: 'operationName', width: 120 },
    { name: 'operationDesc', width: 150 },
    { name: 'grade', width: 100 },
    { name: 'approverName', width: 120 },
    { name: 'filedMeaning', width: 100 },
    { name: 'createdByRealName', width: 100 },
    { name: 'creationDate', width: 100 },
    {
      name: 'enableFlag',
      editor: record => record.getState('editing'),
      renderer: ({ record }) => {
        return (
          <Tag color={record?.get('enableFlag') === 'Y' ? 'green' : 'red'}>
            {record?.get('enableFlagDesc')}
          </Tag>
        );
      },
    },
  ];

  return <Table dataSet={ds} columns={columns} queryBar={TableQueryBarType.none} />;
};

export default HistoryDrawer;
