import React, { useEffect, useMemo } from 'react';
import { Table, DataSet, Button, Modal } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import notification from 'utils/notification';
import { observer } from 'mobx-react';
import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { SaveScrapApproval } from './services';
import { tableDS, historyDS } from './stores';
import HistoryDrawer from './HistoryDrawer';

const modelPrompt = 'tarzan.hmes.scrapBarcodeGeneration';

const ScrapApprovalConfiguration = observer(() => {
  const tableDs = useMemo(() => new DataSet({ ...tableDS() }), []);
  const historyDs = useMemo(() => new DataSet({ ...historyDS() }), []);
  const { run: saveScrapApproval } = useRequest(SaveScrapApproval(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    tableDs.query();
  }, []);

  const handleHistory = () => {
    const selectedIds = tableDs.selected.map(item => item.get('keyId'));
    if (selectedIds.length === 0) {
      return;
    }
    historyDs.setQueryParameter('keyIds', selectedIds);
    historyDs.query();
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.history`).d('历史查询'),
      drawer: true,
      style: {
        width: 1000,
      },
      children: <HistoryDrawer ds={historyDs} />,
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      cancelButton: true,
    });
  };

  const handleImport = () => {
    openTab({
      key: `/himp/commentImport/HME.SCRAP_APPROVAL_SET`,
      title: intl.get('tarzan.hmes.scrapBarcodeGeneration.title.import').d('报废审批配置维护导入'),
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: getCurrentOrganizationId(),
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  // 行编辑
  const handleEdit = (record: any) => {
    record.setState('editing', true);
  };

  const handleCancel = (record: any) => {
    if (record.status === 'add') {
      tableDs.remove(record);
    } else {
      record.setState('editing', false);
      tableDs.query();
    }
  };

  const handleSave = async (record: any) => {
    const isValid = await record.validate();
    if (!isValid) {
      notification.warning({
        message: intl.get(`${modelPrompt}.pleaseEnter`).d('请输入必输项！'),
      });
      return;
    }
    const data = record.toData();

    const params = [
      {
        siteId: data.siteId,
        approvalType: data.approvalType,
        prodLineId: data.prodLineId,
        operationId: data.operationId,
        grade: data.grade,
        filed: data.filed,
        approverIds: Array.isArray(data.approverObj)
          ? data.approverObj.map((item: any) => item.id)
          : data.approverObj
          ? [data.approverObj.id]
          : [],
        enableFlag: data.enableFlag,
        keyId: data.keyId,
      },
    ];
    saveScrapApproval({
      params,
      onSuccess: () => {
        record.setState('editing', false);
        tableDs.query();
      },
    });
  };
  const handleCreate = () => {
    const record = tableDs.create({}, 0);
    record.setState('editing', true);
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      { name: 'siteObj', editor: record => record.getState('editing') },
      { name: 'approvalType', editor: record => record.getState('editing') },
      { name: 'prodLineObj', editor: record => record.getState('editing') },
      { name: 'prodLineName' },
      { name: 'operationObj', editor: record => record.getState('editing') },
      { name: 'operationDesc' },
      { name: 'grade', editor: record => record.getState('editing') },
      { name: 'filed', editor: record => record.getState('editing') },
      { name: 'approverObj', editor: record => record.getState('editing') },
      {
        name: 'enableFlag',
        editor: record => record.getState('editing'),
        renderer: ({ record }) => {
          return (
            <Tag color={record?.get('enableFlag') === 'Y' ? 'green' : 'red'}>
              {record?.get('enableFlagDesc')}
            </Tag>
          );
        },
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        width: 160,
        align: 'center' as ColumnAlign,
        renderer: (props: any) => {
          const { record } = props;
          const isEditing = record?.getState('editing');
          return isEditing ? (
            <>
              <Button
                color={ButtonColor.primary}
                style={{ marginRight: 8 }}
                onClick={() => handleSave(record)}
                funcType={FuncType.flat}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button onClick={() => handleCancel(record)} funcType={FuncType.flat}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <>
              <Button
                onClick={() => handleEdit(record)}
                icon="edit-o"
                funcType={FuncType.flat}
                style={{ marginRight: 8 }}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
            </>
          );
        },
      },
    ],
    [],
  );

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('报废&设备解锁审批配置维护')}>
        <Button icon="history" onClick={handleHistory} disabled={!tableDs.selected.length}>
          {intl.get('tarzan.common.button.history').d('历史查询')}
        </Button>
        <Button icon="cloud_upload-o" onClick={handleImport}>
          {intl.get('tarzan.common.button.import').d('导入')}
        </Button>
        <Button color={ButtonColor.primary} icon="add" onClick={handleCreate}>
          {intl.get('tarzan.common.button.created').d('新建')}
        </Button>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{ fuzzyQuery: false }}
          dataSet={tableDs}
          columns={columns}
          searchCode="scrapApprovalConfiguration"
          customizedCode="scrapApprovalConfiguration"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.scrapBarcodeGeneration', 'tarzan.common'],
})(ScrapApprovalConfiguration);
