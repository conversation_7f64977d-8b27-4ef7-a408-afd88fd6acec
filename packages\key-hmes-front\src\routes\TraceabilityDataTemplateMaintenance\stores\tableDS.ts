import intl from 'utils/intl';
import { FieldType, FieldIgnore, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { AxiosRequestConfig } from 'axios';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'hmes.traceabilityDataTemplateMaintenance';

const HeaderDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: DataSetSelection.single,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'headId',
  transport: {
    read: (config: AxiosRequestConfig): AxiosRequestConfig => {
      const { data, params } = config;
      return {
        ...config,
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-trace-templates/head/query`,
        method: 'POST',
        data,
        params,
      };
    },
  },
  queryFields: [
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'materialObj.materialId',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      lookupCode: 'MT.ENABLE_FLAG',
    },
    {
      name: 'customerObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerCode',
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      type: FieldType.string,
      bind: 'customerObj.customerId',
    },
  ],
  fields: [
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObj.siteCode',
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'HME.PERMISSION_MATERIAL',
      required: true,
      ignore: FieldIgnore.always,
      textField: 'materialCode',
      cascadeMap: { siteId: 'siteId' },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'materialObj.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialObj.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialObj.materialName',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionFlag`).d('版本标识'),
      bind: 'materialObj.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('revisionFlag') === 'N';
        },
        required: ({ record }) => {
          return record.get('revisionFlag') === 'Y';
        },
      },
    },
    {
      name: 'customerObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      type: FieldType.string,
      bind: 'customerObj.customerId',
    },
    {
      name: 'customerCode',
      type: FieldType.string,
      bind: 'customerObj.customerCode',
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户名称'),
      bind: 'customerObj.customerName',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      required: true,
    },
    {
      name: 'createdByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdByObj`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      valueField: 'id',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      bind: 'createdByObj.id',
    },
    {
      name: 'createdByRealName',
      type: FieldType.string,
      bind: 'createdByObj.realName',
      readOnly: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByRealName',
      label: intl.get(`${modelPrompt}.lastUpdatedByObj`).d('最后更新人'),
      type: FieldType.string,
    },
    {
      name: 'lastUpdatedDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdatedDate`).d('最后更新时间'),
    },
    {
      name: 'editFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editFlag`).d('是否更新标识'),
      defaultValue: 'N',
    },
  ],
  events: {
    update: ({ record, name }) => {
      if (name !== 'editFlag' && record.get('headId')) {
        record.set('editFlag', 'Y');
      }
    },
  },
});

const LineDS = (): DataSetProps => ({
  autoQuery: false,
  pageSize: 10,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'lineId',
  selection: false,
  transport: {
    read: (config: AxiosRequestConfig): AxiosRequestConfig => {
      const { data, params } = config;
      // @ts-ignore
      const { headId } = params;
      return {
        ...config,
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-trace-templates/line/query`,
        method: 'GET',
        data,
        params: {
          ...params,
          headId,
        },
      };
    },
  },
  fields: [
    {
      name: 'paraTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.paraTypeObj`).d('参数类型'),
      required: true,
      lookupCode: 'HME.TRACE_TEMPLATE_NEWS_TYPE',
      valueField: 'value',
      textField: 'meaning',
      multiple: true,
    },
    {
      name: 'paraType',
      type: FieldType.string,
      multiple: true,
    },
    {
      name: 'paraName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paraName`).d('参数名称'),
      dynamicProps: {
        required: ({ record }) => {
          const paraType = record.get('paraType');
          return Array.isArray(paraType)
            ? paraType.some(type => type.includes('FIXED_VALUE'))
            : paraType && paraType.includes('FIXED_VALUE');
        },
      },
    },
    {
      name: 'paraValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paraValue`).d('参数值'),
      dynamicProps: {
        required: ({ record }) => {
          const paraType = record.get('paraType');
          return Array.isArray(paraType)
            ? paraType.some(type => type.includes('FIXED_VALUE'))
            : paraType && paraType.includes('FIXED_VALUE');
        },
      },
    },
    {
      name: 'newsType',
      type: FieldType.string,
      multiple: true,
    },
    {
      name: 'operationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
      lovCode: 'MT.METHOD.OPERATION',
      dynamicProps: {
        required: ({ record }) => {
          const paraType = record.get('paraType');
          if (Array.isArray(paraType)) {
            return paraType.some(type => type.includes('TAG_VALUE') || type.includes('CAL_RESULT'));
          }
          return paraType && (paraType.includes('TAG_VALUE') || paraType.includes('CAL_RESULT'));
        },
      },
      textField: 'operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      type: FieldType.string,
      bind: 'operationObj.operationId',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      bind: 'operationObj.operationName',
    },
    {
      name: 'operationDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationDescription`).d('工艺描述'),
      bind: 'operationObj.description',
    },
    {
      name: 'tagObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagCode`).d('收集项编码'),
      dynamicProps: {
        required: ({ record }) => {
          const paraType = record.get('paraType');
          if (Array.isArray(paraType)) {
            return paraType.some(type => type.includes('TAG_VALUE') || type.includes('CAL_RESULT'));
          }
          return paraType && (paraType.includes('TAG_VALUE') || paraType.includes('CAL_RESULT'));
        },
      },
      textField: 'tagCode',
      lovCode: 'HME_MT_TAG',
      ignore: FieldIgnore.always,
    },
    {
      name: 'tagId',
      type: FieldType.string,
      bind: 'tagObj.tagId',
    },
    {
      name: 'tagCode',
      type: FieldType.string,
      bind: 'tagObj.tagCode',
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      bind: 'tagObj.tagDescription',
      label: intl.get(`${modelPrompt}.tagDescription`).d('收集项描述'),
    },

    {
      name: 'upperLimitValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.upperLimitValue`).d('上限值'),
    },
    {
      name: 'lowerLimitValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lowerLimitValue`).d('下限值'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      required: true,
    },
    {
      name: 'createdByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdByObj`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      valueField: 'id',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      bind: 'createdByObj.id',
    },
    {
      name: 'createdByRealName',
      type: FieldType.string,
      bind: 'createdByObj.realName',
      readOnly: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByRealName',
      label: intl.get(`${modelPrompt}.lastUpdatedByObj`).d('最后更新人'),
      type: FieldType.string,
    },
    {
      name: 'lastUpdatedDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdatedDate`).d('最后更新时间'),
    },
    {
      name: 'editFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editFlag`).d('是否更新标识'),
      defaultValue: 'N',
    },
  ],
  events: {
    update: ({ record, name, value }) => {
      if (name === 'paraTypeObj') {
        let paraTypeValues: any[] = [];
        if (value && Array.isArray(value)) {
          paraTypeValues = value.map((item: any) => {
            if (typeof item === 'object' && item.value) {
              return item.value;
            }
            return item;
          });
        } else if (value) {
          paraTypeValues = [value];
        }
        record.set('paraType', paraTypeValues);
      }

      if (name !== 'editFlag' && record.get('lineId')) {
        record.set('editFlag', 'Y');
      }
    },
  },
});

// 头历史查询DataSet
const HeadHistoryDS = (): DataSetProps => ({
  autoQuery: false,
  pageSize: 10,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'headHisId',
  transport: {
    read: (config: AxiosRequestConfig): AxiosRequestConfig => {
      const { data, params } = config;
      // @ts-ignore
      const { headId } = params;
      return {
        ...config,
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-trace-templates/head/his/query`,
        method: 'GET',
        data,
        params: {
          ...params,
          headId,
        },
      };
    },
  },
  fields: [
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      required: true,
      textField: 'siteCode',
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObj.siteCode',
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'HME.PERMISSION_MATERIAL',
      required: true,
      ignore: FieldIgnore.always,
      textField: 'materialCode',
      cascadeMap: { siteId: 'siteId' },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'materialObj.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialObj.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialObj.materialName',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionFlag`).d('版本标识'),
      bind: 'materialObj.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('revisionFlag') === 'N';
        },
        required: ({ record }) => {
          return record.get('revisionFlag') === 'Y';
        },
      },
    },
    {
      name: 'customerObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerObj`).d('客户编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      type: FieldType.string,
      bind: 'customerObj.customerId',
    },
    {
      name: 'customerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerName`).d('客户名称'),
      bind: 'customerObj.customerName',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      required: true,
    },
    {
      name: 'createdByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdByObj`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      valueField: 'id',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      bind: 'createdByObj.id',
    },
    {
      name: 'createdByRealName',
      type: FieldType.string,
      bind: 'createdByObj.realName',
      readOnly: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.lastUpdatedByObj`).d('最后更新人'),
      lovCode: 'MT.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      valueField: 'id',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'lastUpdatedBy',
      type: FieldType.string,
      bind: 'lastUpdatedByObj.id',
    },
    {
      name: 'lastUpdatedByRealName',
      type: FieldType.string,
      bind: 'lastUpdatedByObj.realName',
      readOnly: true,
    },
    {
      name: 'lastUpdatedDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdatedDate`).d('最后更新时间'),
    },
  ],
});

// 行历史查询DataSet
const LineHistoryDS = (): DataSetProps => ({
  autoQuery: false,
  pageSize: 10,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'lineHisId',
  transport: {
    read: (config: AxiosRequestConfig): AxiosRequestConfig => {
      const { data, params } = config;
      // @ts-ignore
      const { headId } = params;
      return {
        ...config,
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-trace-templates/line/his/query`,
        method: 'GET',
        data,
        params: {
          ...params,
          headId,
        },
      };
    },
  },
  fields: [
    {
      name: 'paraTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.paraTypeObj`).d('参数类型'),
      required: true,
      lookupCode: 'HME.TRACE_TEMPLATE_NEWS_TYPE',
      valueField: 'value',
      textField: 'meaning',
      multiple: true,
    },
    {
      name: 'paraType',
      type: FieldType.string,
      multiple: true,
    },
    {
      name: 'paraName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paraName`).d('参数名称'),
      dynamicProps: {
        required: ({ record }) => {
          const paraType = record.get('paraType');
          return Array.isArray(paraType)
            ? paraType.some(type => type.includes('FIXED_VALUE'))
            : paraType && paraType.includes('FIXED_VALUE');
        },
      },
    },
    {
      name: 'paraValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paraValue`).d('参数值'),
      dynamicProps: {
        required: ({ record }) => {
          const paraType = record.get('paraType');
          return Array.isArray(paraType)
            ? paraType.some(type => type.includes('FIXED_VALUE'))
            : paraType && paraType.includes('FIXED_VALUE');
        },
      },
    },
    {
      name: 'operationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
      lovCode: 'MT.METHOD.OPERATION',
      dynamicProps: {
        required: ({ record }) => {
          const paraType = record.get('paraType');
          if (Array.isArray(paraType)) {
            return paraType.some(type => type === 'TAG_VALUE' || type === 'CAL_RESULT');
          }
          return paraType === 'TAG_VALUE' || paraType === 'CAL_RESULT';
        },
      },
      textField: 'operationName',
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationId',
      type: FieldType.string,
      bind: 'operationObj.operationId',
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
      bind: 'operationObj.operationName',
    },
    {
      name: 'operationDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationDescription`).d('工艺描述'),
      bind: 'operationObj.description',
    },
    {
      name: 'tagObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.tagCode`).d('收集项编码'),
      dynamicProps: {
        required: ({ record }) => {
          const paraType = record.get('paraType');
          if (Array.isArray(paraType)) {
            return paraType.some(type => type === 'TAG_VALUE' || type === 'CAL_RESULT');
          }
          return paraType === 'TAG_VALUE' || paraType === 'CAL_RESULT';
        },
      },
      textField: 'tagCode',
      lovCode: 'HME_MT_TAG',
      ignore: FieldIgnore.always,
    },
    {
      name: 'tagId',
      type: FieldType.string,
      bind: 'tagObj.tagId',
    },
    {
      name: 'tagCode',
      type: FieldType.string,
      bind: 'tagObj.tagCode',
    },
    {
      name: 'tagDescription',
      type: FieldType.string,
      bind: 'tagObj.tagDescription',
      label: intl.get(`${modelPrompt}.tagDescription`).d('收集项描述'),
    },
    {
      name: 'newsType',
      bind: 'paraTypeObj.value',
      multiple: true,
    },
    {
      name: 'upperLimitValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.upperLimitValue`).d('上限值'),
    },
    {
      name: 'lowerLimitValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lowerLimitValue`).d('下限值'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
      required: true,
    },
    {
      name: 'createdByObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdByObj`).d('创建人'),
      lovCode: 'HADM.USER',
      ignore: FieldIgnore.always,
    },
    {
      name: 'createdBy',
      type: FieldType.string,
      bind: 'createdByObj.id',
    },
    {
      name: 'createdByRealName',
      type: FieldType.string,
      bind: 'createdByObj.realName',
      readOnly: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
  ],
  events: {
    update: ({ record, name, value }) => {
      if (name === 'paraTypeObj') {
        let paraTypeValues: any[] = [];
        if (value && Array.isArray(value)) {
          paraTypeValues = value.map((item: any) => {
            if (typeof item === 'object' && item.value) {
              return item.value;
            }
            return item;
          });
        } else if (value) {
          paraTypeValues = [value];
        }
        record.set('paraType', paraTypeValues);
      }
    },
  },
});

export { HeaderDS, LineDS, HeadHistoryDS, LineHistoryDS };
