import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { FieldType, DataSetSelection, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.scrapBarcodeGeneration';

const tableDS = (): DataSetProps => ({
  name: 'tableDS',
  primaryKey: 'keyId',
  paging: true,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: DataSetSelection.multiple,
  fields: [
    { name: 'keyId', type: FieldType.number },
    {
      name: 'siteObj',
      type: FieldType.object,
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.siteObj`).d('站点编码'),
      required: true,
    },
    { name: 'siteId', type: FieldType.string, bind: 'siteObj.siteId' },
    { name: 'siteCode', type: FieldType.string, bind: 'siteObj.siteCode' },
    {
      name: 'approvalType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.approvalType`).d('审批类型'),
      lookupCode: 'HME.SCRAP_APPROVAL_TYPE',
      required: true,
    },
    {
      name: 'prodLineObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
      lovCode: 'HME.PERMISSION_PROD_LINE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    { name: 'prodLineId', type: FieldType.string, bind: 'prodLineObj.prodLineId' },
    { name: 'prodLineCode', type: FieldType.string, bind: 'prodLineObj.prodLineCode' },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
      bind: 'prodLineObj.prodLineName',
    },
    {
      name: 'operationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationObj`).d('工艺编码'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      textField: 'operationName',
    },
    { name: 'operationId', type: FieldType.string, bind: 'operationObj.operationId' },
    {
      name: 'operationName',
      type: FieldType.string,
      bind: 'operationObj.operationName',
    },
    {
      name: 'operationDesc',
      type: FieldType.string,
      bind: 'operationObj.description',
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺名称'),
    },
    {
      name: 'grade',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.grade`).d('审批层级'),
      lookupCode: 'HME.SCRAP_APPROVAL_GRADE',
      required: true,
      dynamicProps: {
        defaultValue: ({ record }) => {
          const approvalType = record.get('approvalType');
          return approvalType === 'UNLOCK_APPROVAL' ? 'R1' : undefined;
        },
        disabled: ({ record }) => {
          const approvalType = record.get('approvalType');
          return approvalType === 'UNLOCK_APPROVAL';
        },
      },
    },
    {
      name: 'approverObj',
      type: FieldType.object,
      lovCode: 'MT.USER.ORG',
      multiple: true,
      label: intl.get(`${modelPrompt}.approverObj`).d('审批人'),
      ignore: FieldIgnore.always,
      required: true,
      valueField: 'id',
      textField: 'loginName',
    },
    { name: 'approverId', type: FieldType.string, bind: 'approverObj.id' },
    { name: 'approverName', type: FieldType.string, bind: 'approverObj.loginName' },
    {
      name: 'filed',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.filed`).d('业务领域'),
      lookupCode: 'HME.UNLOCK_APPROVAL_FIELD',
      required: true,
    },
    {
      name: 'enableFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  events: {
    update: ({ record, name, value }) => {
      // 当审批类型改变时，自动设置审批层级
      if (name === 'approvalType') {
        if (value === 'UNLOCK_APPROVAL') {
          record.set('grade', 'R1');
        } else {
          // 如果不是UNLOCK_APPROVAL，清空grade让用户重新选择
          record.set('grade', null);
        }
      }
    },
  },
  queryFields: [
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      ignore: FieldIgnore.always,
    },
    { name: 'siteId', type: FieldType.string, bind: 'siteObj.siteId' },
    {
      name: 'approvalType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.approvalType`).d('审批类型'),
      lookupCode: 'HME.SCRAP_APPROVAL_TYPE',
    },
    {
      name: 'prodLineObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
      lovCode: 'HME.PERMISSION_PROD_LINE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    { name: 'prodLineId', type: FieldType.string, bind: 'prodLineObj.prodLineId' },
    {
      name: 'operationObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationCode`).d('工艺编码'),
      lovCode: 'MT.METHOD.OPERATION',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
    },
    { name: 'operationId', type: FieldType.string, bind: 'operationObj.operationId' },
    {
      name: 'grade',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.grade`).d('审批层级'),
      lookupCode: 'HME.SCRAP_APPROVAL_GRADE',
    },
    {
      name: 'approverObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.approverName`).d('审批人'),
      lovCode: 'MT.USER.ORG',
      multiple: true,
      ignore: FieldIgnore.always,
    },
    { name: 'approverIds', type: FieldType.string, bind: 'approverObj.id' },
    {
      name: 'filed',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.filed`).d('业务领域'),
      lookupCode: 'HME.SCRAP_APPROVAL_FIELD',
    },
  ],
  transport: {
    read: config => ({
      ...config,
      url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-scrap-approval-sets/query/ui`,
      method: 'POST',
      transformResponse: (data: any) => {
        if (!data) {
          return null;
        }
        const parsedData = JSON.parse(data);
        if (parsedData.rows && parsedData.rows.content) {
          parsedData.rows.content.forEach((record: any) => {
            const rec = record;
            if (rec.approverId && typeof rec.approverId === 'string') {
              rec.approverId = rec.approverId.split(',');
            }
            if (rec.approverName && typeof rec.approverName === 'string') {
              rec.approverName = rec.approverName.split(',');
            }
          });
        }
        return parsedData;
      },
    }),
  },
});

const historyDS = (): DataSetProps => ({
  name: 'historyDS',
  primaryKey: 'keyId',
  paging: true,
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: DataSetSelection.multiple,
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'approvalTypeMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.approvalTypeMeaning`).d('审批类型'),
    },
    {
      name: 'prodLineCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineCode`).d('产线编码'),
    },
    {
      name: 'prodLineName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prodLineName`).d('产线名称'),
    },
    {
      name: 'operationName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationName`).d('工艺编码'),
    },
    {
      name: 'operationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationDesc`).d('工艺名称'),
    },
    {
      name: 'grade',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.grade`).d('审批层级'),
    },
    {
      name: 'approverName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.approverName`).d('审批人'),
    },
    {
      name: 'filedMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.filedMeaning`).d('业务领域'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'updateByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.updateByName`).d('更新人'),
    },
    {
      name: 'createdByRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByRealName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建日期'),
    },
    {
      name: 'updateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.updateDate`).d('更新时间'),
    },
  ],
  transport: {
    read: config => ({
      url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/hme-scrap-approval-sets/his-query/ui`,
      method: 'POST',
      data: config.data,
    }),
  },
});

export { tableDS, historyDS };
