import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

import { BASIC } from '@utils/config';
import { getResponse } from '@utils/utils';

const modelPrompt = 'tarzan.model.roll.bottom.scrapping.maintenance';

const tenantId = getCurrentOrganizationId();

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoQueryAfterSubmit: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      ignore: FieldIgnore.always,
      textField: 'materialCode',
      valueField: 'materialId',
      dynamicProps: {
        lovPara: ({ record }) => {
          const siteId = record?.get('siteId');
          return {
            tenantId: getCurrentOrganizationId(),
            siteId,
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'createdPerson',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdPerson`).d('创建人'),
      lovCode: 'HIAM.USER',
      noCache: true,
      ignore: FieldIgnore.always,
      textField: 'loginName',
      valueField: 'id',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'userId',
      type: FieldType.string,
      bind: 'createdPerson.id',
    },
    {
      name: 'ncCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncCodeLov`).d('不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      ignore: FieldIgnore.always,
      textField: 'ncCode',
      valueField: 'ncCodeId',
    },
    {
      name: 'ncCodeId',
      type: FieldType.number,
      bind: 'ncCodeLov.ncCodeId',
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      bind: 'ncCodeLov.ncCode',
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      required: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      ignore: FieldIgnore.always,
      required: true,
      textField: 'materialCode',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId: getCurrentOrganizationId(),
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      bind: 'materialLov.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
    },
    {
      name: 'scrapQty',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.scrapQty`).d('卷底料报废数量'),
    },
    {
      name: 'defaultNcCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.defaultNcCodeLov`).d('默认不良代码'),
      lovCode: 'MT.METHOD.NC_CODE',
      required: true,
      ignore: FieldIgnore.always,
      textField: 'ncCode',
      valueField: 'ncCodeId',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'ncCodeId',
      type: FieldType.number,
      bind: 'defaultNcCodeLov.ncCodeId',
    },
    {
      name: 'ncCode',
      type: FieldType.string,
      bind: 'defaultNcCodeLov.ncCode',
      label: intl.get(`${modelPrompt}.defaultNcCodeLov`).d('默认不良代码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('不良代码描述'),
      bind: 'defaultNcCodeLov.description',
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdName`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最近更新时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-material-scrap/query`,
        method: 'POST',
      };
    },
    submit: ({ data }) => {
      data.forEach(item => {
        delete item.__id;
        delete item._status;
      });
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-material-scrap/save`,
        method: 'POST',
        data,
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
});
